"""
Test script for NEC Gemini Processor

This script processes just a few images to test the setup before running the full batch.
"""

import os
import json
from nec_gemini_processor import NECGeminiProcessor

def test_single_image():
    """Test processing a single image."""
    print("🧪 Testing single image processing...")
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY not set!")
        return False
    
    # Initialize processor
    processor = NECGeminiProcessor(api_key)
    
    # Test with the first image
    test_image = "images/image-pg1.png"
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
        return False
    
    print(f"📸 Processing test image: {test_image}")
    result = processor.process_single_image(test_image)
    
    if result["status"] == "success":
        print("✅ Single image test successful!")
        print("📄 Sample extracted data:")
        print(json.dumps(result["data"], indent=2)[:500] + "...")
        return True
    else:
        print(f"❌ Single image test failed: {result}")
        return False

def test_batch_processing():
    """Test batch processing with a few images."""
    print("\n🧪 Testing batch processing (first 3 images)...")
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY not set!")
        return False
    
    # Initialize processor
    processor = NECGeminiProcessor(api_key)
    
    # Process first 3 images
    results = processor.process_all_images(
        images_folder="images",
        output_file="test_results.json",
        start_page=1,
        end_page=3,
        delay_seconds=2.0  # Longer delay for testing
    )
    
    print(f"\n📊 Test Results:")
    print(f"✅ Successful: {results['summary']['successful']}")
    print(f"❌ Failed: {results['summary']['failed']}")
    print(f"🔧 JSON Errors: {results['summary']['json_errors']}")
    print(f"🌐 API Errors: {results['summary']['api_errors']}")
    
    if results['summary']['successful'] > 0:
        print("✅ Batch processing test successful!")
        print("📄 Check 'test_results.json' for detailed results")
        return True
    else:
        print("❌ Batch processing test failed!")
        return False

def main():
    """Main test function."""
    print("🧪 NEC Gemini Processor Test Suite")
    print("=" * 40)
    
    # Test 1: Single image
    if not test_single_image():
        print("\n❌ Single image test failed. Please check your setup.")
        return
    
    # Test 2: Batch processing
    proceed = input("\n🤔 Single image test passed! Test batch processing? (y/n): ").strip().lower()
    if proceed in ['y', 'yes']:
        if test_batch_processing():
            print("\n🎉 All tests passed! You're ready to process the full document.")
            print("💡 Run 'python setup_and_run.py' to process all images.")
        else:
            print("\n❌ Batch test failed. Please check the errors above.")
    else:
        print("\n👋 Testing complete! Run batch test when ready.")

if __name__ == "__main__":
    main()
