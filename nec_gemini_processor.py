import os
import json
import base64
import time
from pathlib import Path
from typing import List, Dict, Any
import logging
import google.generativeai as genai
from PIL import Image
import io

class NECGeminiProcessor:
    def __init__(self, api_key: str):
        """
        Initialize the NEC Gemini Processor.
        
        Args:
            api_key: Google Gemini API key
        """
        self.api_key = api_key
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-1.5-pro')
        
        # Setup logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        # The detailed prompt for NEC analysis
        self.nec_prompt = """Analyze this image of the National Electrical Code (NEC) document and extract all the numbered subheadings and their corresponding rules/descriptions.

Please format the output as a JSON object with the following structure:

{
  "document_info": {
    "chapter": "Chapter 2",
    "article": "Article 200", 
    "title": "Use and Identification of Grounded Conductors"
  },
  "sections": [
    {
      "section_number": "200.1",
      "title": "Scope",
      "content": "This article provides requirements for the following: (1) Identification of terminals (2) Grounded conductors in premises wiring systems (3) Identification of grounded conductors"
    },
    {
      "section_number": "200.2", 
      "title": "General",
      "content": "Grounded conductors shall comply with 200.2(A) and (B)."
    }
  ]
}

Requirements:
1. Extract ALL numbered sections (like 200.1, 200.2, 200.3, etc.)
2. Include the section title (like "Scope", "General", etc.)
3. Include the complete text content for each section
4. Maintain the exact formatting and numbering as shown in the image
5. Include any subsections (A), (B), (1), (2), etc. within the main content
6. Preserve any exceptions, informational notes, or special formatting
7. If there are multiple paragraphs under a section, include them all in the content field
8. Use proper JSON formatting with proper escaping of quotes and special characters

Please be thorough and accurate in extracting all the regulatory text and section numbers visible in this image."""

    def encode_image(self, image_path: str) -> str:
        """
        Encode image to base64 string.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Base64 encoded image string
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            self.logger.error(f"Error encoding image {image_path}: {e}")
            return ""

    def process_single_image(self, image_path: str, retry_count: int = 3) -> Dict[str, Any]:
        """
        Process a single image with Gemini and extract NEC content.
        
        Args:
            image_path: Path to the image file
            retry_count: Number of retry attempts for API calls
            
        Returns:
            Dictionary containing extracted NEC data or error info
        """
        self.logger.info(f"Processing image: {image_path}")
        
        try:
            # Load and prepare the image
            image = Image.open(image_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            for attempt in range(retry_count):
                try:
                    # Generate content with Gemini
                    response = self.model.generate_content([
                        self.nec_prompt,
                        image
                    ])
                    
                    # Extract the response text
                    response_text = response.text.strip()
                    
                    # Try to parse as JSON
                    if response_text.startswith('```json'):
                        # Remove markdown code block formatting
                        response_text = response_text.replace('```json', '').replace('```', '').strip()
                    
                    # Parse JSON response
                    parsed_data = json.loads(response_text)
                    
                    self.logger.info(f"Successfully processed {image_path}")
                    return {
                        "status": "success",
                        "image_path": image_path,
                        "data": parsed_data
                    }
                    
                except json.JSONDecodeError as e:
                    self.logger.warning(f"JSON decode error for {image_path} (attempt {attempt + 1}): {e}")
                    if attempt == retry_count - 1:
                        return {
                            "status": "json_error",
                            "image_path": image_path,
                            "error": str(e),
                            "raw_response": response_text
                        }
                    time.sleep(2)  # Wait before retry
                    
                except Exception as e:
                    self.logger.warning(f"API error for {image_path} (attempt {attempt + 1}): {e}")
                    if attempt == retry_count - 1:
                        return {
                            "status": "api_error",
                            "image_path": image_path,
                            "error": str(e)
                        }
                    time.sleep(5)  # Wait longer for API errors
                    
        except Exception as e:
            self.logger.error(f"Error processing image {image_path}: {e}")
            return {
                "status": "error",
                "image_path": image_path,
                "error": str(e)
            }

    def process_all_images(self, images_folder: str, output_file: str = "nec_extracted_data.json", 
                          start_page: int = 1, end_page: int = None, delay_seconds: float = 1.0) -> Dict[str, Any]:
        """
        Process all images in the folder and extract NEC content.
        
        Args:
            images_folder: Path to folder containing images
            output_file: Output JSON file name
            start_page: Starting page number (1-based)
            end_page: Ending page number (1-based), None for all pages
            delay_seconds: Delay between API calls to avoid rate limiting
            
        Returns:
            Dictionary containing processing results
        """
        images_path = Path(images_folder)
        
        if not images_path.exists():
            self.logger.error(f"Images folder not found: {images_folder}")
            return {"status": "error", "message": "Images folder not found"}
        
        # Get all image files and sort them
        image_files = sorted([f for f in images_path.glob("image-pg*.png")])
        
        if not image_files:
            self.logger.error("No image files found in the folder")
            return {"status": "error", "message": "No image files found"}
        
        # Filter by page range if specified
        if start_page > 1 or end_page is not None:
            filtered_files = []
            for img_file in image_files:
                # Extract page number from filename
                try:
                    page_num = int(img_file.stem.replace("image-pg", ""))
                    if page_num >= start_page and (end_page is None or page_num <= end_page):
                        filtered_files.append(img_file)
                except ValueError:
                    continue
            image_files = filtered_files
        
        self.logger.info(f"Processing {len(image_files)} images...")
        
        results = {
            "processing_info": {
                "total_images": len(image_files),
                "start_page": start_page,
                "end_page": end_page,
                "processed_at": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "extracted_data": [],
            "errors": [],
            "summary": {
                "successful": 0,
                "failed": 0,
                "json_errors": 0,
                "api_errors": 0
            }
        }
        
        for i, image_file in enumerate(image_files, 1):
            self.logger.info(f"Processing image {i}/{len(image_files)}: {image_file.name}")
            
            result = self.process_single_image(str(image_file))
            
            if result["status"] == "success":
                results["extracted_data"].append(result["data"])
                results["summary"]["successful"] += 1
            else:
                results["errors"].append(result)
                if result["status"] == "json_error":
                    results["summary"]["json_errors"] += 1
                elif result["status"] == "api_error":
                    results["summary"]["api_errors"] += 1
                else:
                    results["summary"]["failed"] += 1
            
            # Add delay to avoid rate limiting
            if i < len(image_files):
                time.sleep(delay_seconds)
        
        # Save results to file
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Results saved to {output_file}")
        except Exception as e:
            self.logger.error(f"Error saving results: {e}")
        
        # Print summary
        self.logger.info(f"Processing complete!")
        self.logger.info(f"Successful: {results['summary']['successful']}")
        self.logger.info(f"Failed: {results['summary']['failed']}")
        self.logger.info(f"JSON Errors: {results['summary']['json_errors']}")
        self.logger.info(f"API Errors: {results['summary']['api_errors']}")
        
        return results

def main():
    """
    Main function to run the NEC Gemini processor.
    """
    # Configuration - API key included directly
    API_KEY = "AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k"

    # Fallback to environment variable if needed
    if not API_KEY:
        API_KEY = os.getenv("GEMINI_API_KEY")
        if not API_KEY:
            print("Error: Please set the GEMINI_API_KEY environment variable")
            print("You can get an API key from: https://makersuite.google.com/app/apikey")
            return
    
    # Initialize processor
    processor = NECGeminiProcessor(API_KEY)
    
    # Process images
    images_folder = "images"
    output_file = "nec_extracted_data.json"
    
    # You can customize these parameters:
    start_page = 1          # Start from page 1
    end_page = None         # Process all pages (set to a number to limit)
    delay_seconds = 1.0     # Delay between API calls
    
    # For testing, you might want to process just a few pages first:
    # start_page = 1
    # end_page = 5
    
    results = processor.process_all_images(
        images_folder=images_folder,
        output_file=output_file,
        start_page=start_page,
        end_page=end_page,
        delay_seconds=delay_seconds
    )
    
    print(f"\nProcessing completed! Check {output_file} for results.")

if __name__ == "__main__":
    main()
