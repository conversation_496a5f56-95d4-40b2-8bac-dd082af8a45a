# NEC Gemini Processor

This project processes National Electrical Code (NEC) document images using Google's Gemini AI to extract structured JSON data with all numbered sections, titles, and content.

## 🚀 Quick Start

### 1. Get Gemini API Key
1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Set the environment variable:
   ```bash
   # Windows
   set GEMINI_API_KEY=your_api_key_here
   
   # Linux/Mac
   export GEMINI_API_KEY=your_api_key_here
   ```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Run the Processor

#### Option A: Automated Setup (Recommended)
```bash
python setup_and_run.py
```

#### Option B: Test First (Recommended for first-time users)
```bash
python test_processor.py
```

#### Option C: Direct Processing
```bash
python nec_gemini_processor.py
```

## 📁 Project Structure

```
├── images/                     # Folder containing NEC page images
│   ├── image-pg1.png
│   ├── image-pg2.png
│   └── ...
├── nec_gemini_processor.py     # Main processor script
├── setup_and_run.py           # Automated setup and run script
├── test_processor.py          # Test script for validation
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## 🔧 Configuration Options

### Processing Range
You can process specific page ranges:

```python
# Process pages 1-10 only
results = processor.process_all_images(
    images_folder="images",
    start_page=1,
    end_page=10
)

# Process all pages
results = processor.process_all_images(
    images_folder="images",
    start_page=1,
    end_page=None
)
```

### API Rate Limiting
Adjust delay between API calls:

```python
# 2 second delay between calls (recommended for large batches)
delay_seconds=2.0

# 1 second delay (default)
delay_seconds=1.0
```

## 📄 Output Format

The processor generates JSON output with this structure:

```json
{
  "processing_info": {
    "total_images": 917,
    "start_page": 1,
    "end_page": null,
    "processed_at": "2024-01-15 10:30:00"
  },
  "extracted_data": [
    {
      "document_info": {
        "chapter": "Chapter 2",
        "article": "Article 200",
        "title": "Use and Identification of Grounded Conductors"
      },
      "sections": [
        {
          "section_number": "200.1",
          "title": "Scope",
          "content": "This article provides requirements for..."
        }
      ]
    }
  ],
  "errors": [],
  "summary": {
    "successful": 850,
    "failed": 67,
    "json_errors": 5,
    "api_errors": 2
  }
}
```

## 🛠️ Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```
   Error: Please set the GEMINI_API_KEY environment variable
   ```
   Solution: Set your Gemini API key as shown in step 1.

2. **Rate Limiting**
   ```
   API error: Rate limit exceeded
   ```
   Solution: Increase `delay_seconds` parameter or try again later.

3. **JSON Parse Errors**
   ```
   JSON decode error
   ```
   Solution: These are usually due to complex formatting in the image. The script will retry automatically.

4. **Images Not Found**
   ```
   Images folder not found
   ```
   Solution: Ensure the `images` folder exists with PNG files.

### Performance Tips

1. **Start Small**: Test with a few pages first using `test_processor.py`
2. **Batch Processing**: For large documents, consider processing in smaller batches
3. **Monitor Progress**: The script logs progress and saves results incrementally
4. **Error Handling**: Failed images are logged but don't stop the entire process

## 📊 Expected Processing Time

- **Single Image**: 3-10 seconds
- **100 Images**: 10-30 minutes
- **917 Images**: 2-6 hours (depending on API response times)

## 🔍 Quality Assurance

The processor includes several quality checks:
- JSON validation for each response
- Retry logic for failed API calls
- Detailed error logging
- Progress tracking
- Summary statistics

## 💡 Tips for Best Results

1. **Image Quality**: Ensure images are clear and readable
2. **API Limits**: Monitor your Gemini API usage and quotas
3. **Backup**: Save intermediate results regularly
4. **Validation**: Review extracted data for accuracy
5. **Incremental Processing**: Process in smaller batches for large documents

## 🤝 Support

If you encounter issues:
1. Check the error logs in the console output
2. Review the `errors` section in the output JSON
3. Try processing a smaller batch first
4. Ensure your API key has sufficient quota

## 📝 License

This project is for educational and research purposes. Please ensure compliance with NEC document usage rights and Google's API terms of service.
