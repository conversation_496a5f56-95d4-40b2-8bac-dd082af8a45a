"""
Check the progress of NEC processing and look for any output files.
"""

import os
import json
from pathlib import Path

def check_output_files():
    """Check for any JSON output files."""
    print("🔍 Checking for output files...")
    
    current_dir = Path(".")
    json_files = list(current_dir.glob("*.json"))
    
    if json_files:
        print(f"📄 Found {len(json_files)} JSON files:")
        for json_file in json_files:
            print(f"  - {json_file.name} ({json_file.stat().st_size} bytes)")
            
            # Try to read and show summary
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'processing_info' in data:
                    print(f"    📊 Processing info found")
                    print(f"    📈 Total images: {data['processing_info'].get('total_images', 'N/A')}")
                    print(f"    ✅ Successful: {data.get('summary', {}).get('successful', 'N/A')}")
                    print(f"    ❌ Failed: {data.get('summary', {}).get('failed', 'N/A')}")
                    print(f"    📝 Extracted data entries: {len(data.get('extracted_data', []))}")
                    print(f"    🚨 Errors: {len(data.get('errors', []))}")
                
            except Exception as e:
                print(f"    ⚠️ Error reading file: {e}")
    else:
        print("❌ No JSON output files found yet")
    
    return json_files

def check_expected_files():
    """Check for expected output files."""
    print("\n🎯 Checking for expected output files...")
    
    expected_files = [
        "nec_extracted_data.json",
        "test_results.json"
    ]
    
    for filename in expected_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"✅ {filename} exists ({size} bytes)")
        else:
            print(f"❌ {filename} not found")

def show_recent_activity():
    """Show recently modified files."""
    print("\n📅 Recently modified files (last 1 hour):")
    
    import time
    current_time = time.time()
    one_hour_ago = current_time - 3600  # 1 hour in seconds
    
    current_dir = Path(".")
    recent_files = []
    
    for file_path in current_dir.iterdir():
        if file_path.is_file():
            mod_time = file_path.stat().st_mtime
            if mod_time > one_hour_ago:
                recent_files.append((file_path, mod_time))
    
    # Sort by modification time (newest first)
    recent_files.sort(key=lambda x: x[1], reverse=True)
    
    if recent_files:
        for file_path, mod_time in recent_files[:10]:  # Show top 10
            mod_time_str = time.strftime("%H:%M:%S", time.localtime(mod_time))
            print(f"  {mod_time_str} - {file_path.name}")
    else:
        print("  No recently modified files found")

def main():
    """Main function to check processing progress."""
    print("🔍 NEC Processing Progress Check")
    print("=" * 40)
    
    # Check for output files
    json_files = check_output_files()
    
    # Check for expected files
    check_expected_files()
    
    # Show recent activity
    show_recent_activity()
    
    # Provide guidance
    print("\n💡 What this means:")
    if not json_files:
        print("  - Processing is either still running or failed early")
        print("  - JSON data is only saved when processing completes")
        print("  - Check the console output for current status")
    else:
        print("  - Some JSON output has been generated")
        print("  - Check the files above for extracted data")
    
    print("\n🚀 Next steps:")
    print("  - If processing is still running: wait for completion")
    print("  - If processing failed: run the updated script with JSON fixes")
    print("  - To restart: python nec_gemini_processor.py")

if __name__ == "__main__":
    main()
