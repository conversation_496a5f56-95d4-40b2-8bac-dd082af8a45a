import os
import fitz  # PyMuPDF
from pathlib import Path
from typing import List
import logging

def convert_pdf_to_images(pdf_path: str, dpi: int = 150) -> List[str]:
    """
    Convert all pages of the PDF to images and save them in the images folder.

    Args:
        pdf_path: Path to the PDF file.
        dpi: Resolution of the images.

    Returns:
        List of saved image file paths.
    """
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Save images to the images folder
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = Path(script_dir) / "images"
    output_path.mkdir(parents=True, exist_ok=True)
    image_paths = []

    try:
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        logger.info(f"Converting {total_pages} pages from PDF: {pdf_path}")

        for page_number in range(total_pages):
            page = doc.load_page(page_number)  # 0-based index
            pix = page.get_pixmap(dpi=dpi)

            # Save with naming convention: image-pg1, image-pg2, etc.
            image_filename = f"image-pg{page_number + 1}.png"
            image_path = output_path / image_filename
            pix.save(str(image_path))
            image_paths.append(str(image_path))
            logger.info(f"Saved page {page_number + 1} to {image_filename}")

        doc.close()
        logger.info(f"Successfully converted {len(image_paths)} pages to images")
        return image_paths

    except Exception as e:
        logger.error(f"Error converting PDF to images: {e}")
        return []

if __name__ == "__main__":
    # ✅ Replace this with the actual path to your PDF
    pdf_path = "C:/Users/<USER>/Downloads/NEC_2023.pdf"

    extracted_images = convert_pdf_to_images(pdf_path, dpi=150)
    print("Images saved at:")
    for img in extracted_images:
        print(img)
