"""
Debug script to investigate JSON parsing issues with Gemini responses.
"""

import json
from nec_gemini_processor import NECGeminiProcessor

def test_json_cleaning():
    """Test the JSON cleaning function with sample problematic responses."""
    
    processor = NECGeminiProcessor("AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k")
    
    # Test cases that might cause JSON errors
    test_cases = [
        # Case 1: JSON with markdown
        '''```json
{
  "document_info": {
    "chapter": "Chapter 1",
    "article": "Article 100"
  }
}
```

This is additional text that causes the error.''',
        
        # Case 2: JSON with extra text
        '''{
  "document_info": {
    "chapter": "Chapter 1"
  }
}

Additional explanation text here.''',
        
        # Case 3: Multiple JSON objects
        '''{
  "document_info": {
    "chapter": "Chapter 1"
  }
}
{
  "extra": "data"
}'''
    ]
    
    print("🧪 Testing JSON cleaning function...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Original (first 100 chars): {test_case[:100]}...")
        
        try:
            cleaned = processor._clean_json_response(test_case)
            print(f"Cleaned: {cleaned}")
            
            # Try to parse
            parsed = json.loads(cleaned)
            print("✅ Successfully parsed!")
            print(f"Parsed data: {json.dumps(parsed, indent=2)}")
            
        except json.JSONDecodeError as e:
            print(f"❌ Still failed to parse: {e}")
            print(f"Cleaned text: {cleaned}")
        except Exception as e:
            print(f"❌ Other error: {e}")

def test_single_image_debug():
    """Test processing the first image with detailed debugging."""
    
    print("\n🔍 Testing first image with debug info...")
    
    processor = NECGeminiProcessor("AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k")
    
    # Enable debug logging
    import logging
    logging.getLogger().setLevel(logging.DEBUG)
    
    result = processor.process_single_image("images/image-pg1.png", retry_count=1)
    
    print(f"\nResult status: {result['status']}")
    if result['status'] == 'json_error':
        print(f"Error: {result['error']}")
        print(f"Raw response (first 300 chars): {result.get('raw_response', 'N/A')[:300]}...")
        print(f"Cleaned response (first 300 chars): {result.get('cleaned_response', 'N/A')[:300]}...")

if __name__ == "__main__":
    # Test the cleaning function first
    test_json_cleaning()
    
    # Then test with actual image
    proceed = input("\n🤔 Test with actual image? (y/n): ").strip().lower()
    if proceed in ['y', 'yes']:
        test_single_image_debug()
