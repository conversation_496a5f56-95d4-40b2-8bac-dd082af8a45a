"""
Setup and run script for NEC Gemini Processing

This script helps you set up the environment and run the NEC document processor.
"""

import os
import subprocess
import sys
from pathlib import Path

def install_requirements():
    """Install required packages."""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False

def check_api_key():
    """Check if Gemini API key is set."""
    # API key is now included directly in the code
    api_key = "AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k"

    if api_key:
        os.environ["GEMINI_API_KEY"] = api_key
        print("✅ Gemini API key configured!")
        return True

    # Fallback to environment variable check
    env_api_key = os.getenv("GEMINI_API_KEY")
    if not env_api_key:
        print("❌ GEMINI_API_KEY environment variable not set!")
        print("\n📝 To set up your API key:")
        print("1. Get an API key from: https://makersuite.google.com/app/apikey")
        print("2. Set the environment variable:")
        print("   Windows: set GEMINI_API_KEY=your_api_key_here")
        print("   Linux/Mac: export GEMINI_API_KEY=your_api_key_here")
        return False
    else:
        print("✅ GEMINI_API_KEY found!")
        return True

def check_images_folder():
    """Check if images folder exists and has files."""
    images_path = Path("images")
    if not images_path.exists():
        print("❌ Images folder not found!")
        return False
    
    image_files = list(images_path.glob("image-pg*.png"))
    if not image_files:
        print("❌ No image files found in images folder!")
        return False
    
    print(f"✅ Found {len(image_files)} image files in images folder!")
    return True

def run_processor():
    """Run the NEC processor."""
    print("\n🚀 Starting NEC Gemini Processor...")
    
    # Ask user for processing options
    print("\n📋 Processing Options:")
    print("1. Process all images (recommended for full document)")
    print("2. Process a specific range (recommended for testing)")
    
    choice = input("Choose option (1 or 2): ").strip()
    
    if choice == "2":
        try:
            start_page = int(input("Enter start page number: "))
            end_page_input = input("Enter end page number (or press Enter for all remaining): ").strip()
            end_page = int(end_page_input) if end_page_input else None
            
            # Modify the processor script to use these parameters
            print(f"\n🔄 Processing pages {start_page} to {end_page or 'end'}...")
            
        except ValueError:
            print("❌ Invalid page numbers. Using default settings.")
            start_page, end_page = 1, None
    else:
        start_page, end_page = 1, None
        print("\n🔄 Processing all images...")
    
    # Import and run the processor
    try:
        from nec_gemini_processor import NECGeminiProcessor

        # Use the configured API key
        api_key = "AIzaSyDoD_dJdV6P8fliHi6MRling6js0Y9812k"
        processor = NECGeminiProcessor(api_key)
        
        results = processor.process_all_images(
            images_folder="images",
            output_file="nec_extracted_data.json",
            start_page=start_page,
            end_page=end_page,
            delay_seconds=1.0
        )
        
        print("\n✅ Processing completed!")
        print(f"📊 Results: {results['summary']['successful']} successful, {results['summary']['failed']} failed")
        print("📄 Check 'nec_extracted_data.json' for the extracted data")
        
    except ImportError as e:
        print(f"❌ Error importing processor: {e}")
    except Exception as e:
        print(f"❌ Error running processor: {e}")

def main():
    """Main setup and run function."""
    print("🔧 NEC Gemini Processor Setup")
    print("=" * 40)
    
    # Step 1: Install requirements
    if not install_requirements():
        return
    
    # Step 2: Check API key
    if not check_api_key():
        print("\n❌ Cannot proceed without API key. Please set GEMINI_API_KEY and try again.")
        return
    
    # Step 3: Check images folder
    if not check_images_folder():
        print("\n❌ Cannot proceed without images. Please ensure images folder exists with PNG files.")
        return
    
    # Step 4: Run processor
    proceed = input("\n🤔 Ready to start processing? (y/n): ").strip().lower()
    if proceed in ['y', 'yes']:
        run_processor()
    else:
        print("👋 Setup complete! Run 'python nec_gemini_processor.py' when ready.")

if __name__ == "__main__":
    main()
